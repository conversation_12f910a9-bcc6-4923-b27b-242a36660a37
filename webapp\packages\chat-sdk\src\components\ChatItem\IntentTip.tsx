import React, { useEffect, useState } from 'react';
import { CheckCircleFilled, CloseCircleFilled, UpOutlined, DownOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { PREFIX_CLS } from '../../common/constants';
import Loading from './Loading';

const prefixCls = `${PREFIX_CLS}-item`;

type Props = {
  data_query_type: string;
  onComplete?: () => void;
  parseInfo: any;
  errorMsg?: string; // 新增：错误信息
  failed?: boolean; // 新增：是否失败
  enableThinkingChain?: boolean; // 新增：是否启用思维链动画
  userQuestion?: string; // 新增：用户的问题内容
};

const IntentTip: React.FC<Props> = ({
  data_query_type,
  onComplete,
  parseInfo,
  errorMsg,
  failed = false,
  enableThinkingChain = true,
  userQuestion
}) => {
  const [isCompleted, setIsCompleted] = useState(!enableThinkingChain || failed);
  const [hasTriggeredComplete, setHasTriggeredComplete] = useState(false);

  // 当props变化时重置状态
  useEffect(() => {
    setIsCompleted(!enableThinkingChain || failed);
    setHasTriggeredComplete(false);
  }, [data_query_type, enableThinkingChain, failed]);

  useEffect(() => {
    // 防止重复触发
    if (hasTriggeredComplete) {
      return;
    }

    if (failed || !enableThinkingChain) {
      // 失败状态或不启用思维链时立即完成
      setHasTriggeredComplete(true);
      if (onComplete) {
        onComplete();
      }
      return;
    }

    // 启用思维链时，1秒后显示完成状态
    const timer = setTimeout(() => {
      setIsCompleted(true);
      setHasTriggeredComplete(true);
      if (onComplete) {
        onComplete();
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [failed, enableThinkingChain]); // 移除onComplete依赖，防止重复执行

  // 判断是否为意图分类失败（通过data_query_type或failed参数）
  const isIntentFailed = failed || data_query_type === '没有识别到用户意图';

  const [collapsed, setCollapsed] = useState(false);

  return (
    <div className={classNames(`${prefixCls}-parse-tip`, isIntentFailed && `${prefixCls}-parse-tip-failed`)}>
      <div className={`${prefixCls}-title-bar`}>
        {!isIntentFailed ? (
          isCompleted ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <div style={{ width: '16px', height: '16px' }} /> // 占位符，保持布局一致
          )
        ) : (
          <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
        )}
        <div className={`${prefixCls}-step-title`} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          意图分类{isIntentFailed ? '失败' : isCompleted ? '' : '中'}
          {!isIntentFailed && !isCompleted && <Loading />}
        </div>
        {/* 折叠/展开箭头 */}
        <span
          onClick={() => setCollapsed(!collapsed)}
          style={{ cursor: 'pointer', marginLeft: 8, color: 'var(--text-color-third)' }}
        >
          {collapsed ? <DownOutlined /> : <UpOutlined />}
        </span>
      </div>
      {isCompleted && !collapsed && (
        <div className={classNames(
          `${prefixCls}-content-container`,
          isIntentFailed && `${prefixCls}-content-container-failed`
        )}>
          <div className={`${prefixCls}-tip`}>
            <div className={`${prefixCls}-tip-content`}>
                {isIntentFailed ? (
                  <>
                  <div>没有识别到用户意图，请尝试重新描述您的问题</div>
                    {errorMsg && (
                      <div style={{ marginTop: 8, color: '#ff4d4f', fontSize: '12px' }}>
                        错误详情：{errorMsg}
                      </div>
                    )}
                  </>
                ) : (
                  `用户的问题是${userQuestion || ''}，属于${data_query_type}类问题`
                )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntentTip;
