import React, { useMemo, useRef, useState } from "react";
import { Input, But<PERSON>, Tooltip } from "antd";
import { DesktopOutlined, FileWordOutlined, FilePptOutlined, TableOutlined } from '@ant-design/icons';
import classNames from "classnames";
import { TextAreaRef } from "antd/es/input/TextArea";
import { getOS } from "../../utils";

const { TextArea } = Input;

// 图标映射函数
const getProductIcon = (iconName: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'icon-diannao': <DesktopOutlined />,
    'icon-wendang': <FileWordOutlined />,
    'icon-ppt': <FilePptOutlined />,
    'icon-biaoge': <TableOutlined />,
  };
  return iconMap[iconName] || <DesktopOutlined />;
};

type Props = {
  placeholder: string;
  showBtn: boolean;
  disabled: boolean;
  size: string;
  product?: CHAT.Product;
  send: (p: CHAT.TInputInfo) => void;
};

const GeneralInput: GenieType.FC<Props> = (props) => {
  const { placeholder, disabled, product, send } = props;
  const [question, setQuestion] = useState<string>("");
  const [deepThink, setDeepThink] = useState<boolean>(false);
  const textareaRef = useRef<TextAreaRef>(null);
  const tempData = useRef<{
    cmdPress?: boolean;
    compositing?: boolean;
  }>({});

  const questionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuestion(e.target.value);
  };

  const pressEnter: React.KeyboardEventHandler<HTMLTextAreaElement> = () => {
    if (tempData.current.compositing) {
      return;
    }
    // 按住command 回车换行逻辑
    if (tempData.current.cmdPress) {
      const textareaDom = textareaRef.current?.resizableTextArea?.textArea;
      if (!textareaDom) {
        return;
      }
      const { selectionStart, selectionEnd } = textareaDom || {};
      const newValue =
        question.substring(0, selectionStart) +
        '\n' + // 插入换行符
        question.substring(selectionEnd!);

      setQuestion(newValue);
      setTimeout(() => {
        textareaDom.selectionStart = selectionStart! + 1;
        textareaDom.selectionEnd = selectionStart! + 1;
        textareaDom.focus();
      }, 20);
      return;
    }
    // 屏蔽状态，不发
    if (!question || disabled) {
      return;
    }
    send({
      message: question,
      outputStyle: product?.type,
      deepThink,
    });
    setQuestion("");
  };

  const sendMessage = () => {
    send({
      message: question,
      outputStyle: product?.type,
      deepThink,
    });
    setQuestion("");
  };

  return (
    <div className="relative w-full">
      <div className="relative w-full h-[56px] bg-white border border-[#4A72F5] rounded-[8px] transition-all duration-150 ease-in-out hover:border-[#1890ff] hover:shadow-[0_0_0_2px_rgba(24,144,255,0.2)] focus-within:border-[#1890ff] focus-within:shadow-[0_0_0_2px_rgba(24,144,255,0.2)]">
        <TextArea
          ref={textareaRef}
          value={question}
          placeholder={placeholder}
          rows={1}
          autoSize={false}
          className={classNames(
            "w-full h-full border-0 resize-none bg-transparent outline-none px-3 py-0 text-[14px] leading-[32px] flex items-center"
          )}
          style={{
            boxShadow: 'none',
            border: 'none',
            outline: 'none'
          }}
          onChange={questionChange}
          onPressEnter={pressEnter}
          onKeyDown={(event) => {
            tempData.current.cmdPress = event.metaKey || event.ctrlKey;
          }}
          onKeyUp={() => {
            tempData.current.cmdPress = false;
          }}
          onCompositionStart={() => {
            tempData.current.compositing = true;
          }}
          onCompositionEnd={() => {
            tempData.current.compositing = false;
          }}
        />

        {/* 发送按钮 */}
        <div
          className={classNames(
            "absolute right-[10px] bottom-[13px] flex items-center justify-center w-[30px] h-[30px] rounded-full transition-all duration-300 ease-in-out",
            !question || disabled
              ? "bg-[rgb(184,184,191)] cursor-not-allowed"
              : "bg-[#4A72F5] cursor-pointer hover:bg-[#1890ff]"
          )}
          onClick={!question || disabled ? undefined : sendMessage}
        >
          <Tooltip title="发送">
            <i
              className={classNames(
                "font_family icon-fasongtianchong text-white text-[20px]",
                !question || disabled ? "pointer-events-none" : ""
              )}
            ></i>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default GeneralInput;
