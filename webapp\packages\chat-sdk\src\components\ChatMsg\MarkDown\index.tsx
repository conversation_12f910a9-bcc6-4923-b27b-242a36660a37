import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Spin } from 'antd';
import * as echarts from 'echarts';
import { THEME_COLOR_LIST, CLS_PREFIX, CHART_SECONDARY_COLOR, CHART_BLUE_COLOR } from '../../../common/constants';
import rehypeHighlight from 'rehype-highlight';
import remarkGfm from 'remark-gfm';
import Markdown from 'react-markdown';
import 'github-markdown-css/github-markdown.css';
import 'highlight.js/styles/github.css';
import Loading from '../../ChatItem/Loading'
import { getChartLightenColor } from '../../../utils/utils';
import { useStreaming } from '../../../context/StreamingContext';

// 多系列数据接口定义
interface MultiSeriesData {
  name: string;
  data: number[];
}

interface ChartData {
  ECHarts: string;
  chartType: 'bar' | 'pie' | 'line' | 'multiLine' | 'multiBar';
  chartTitle: string;
  chartXAxis?: string[];
  chartData: number[] | Array<{name: string; value: number}> | MultiSeriesData[];
}
  
interface StreamItem {
  type: 'text' | 'chart' | 'loading' | 'dataLoading';
  content: string;
  chartData?: ChartData;
  id: string;
}
  
interface Props {    
  markdown: string;
  fontSize?: number;    
  loading?: boolean;    
  onApplyAuth?: (model: string) => void;  
}    
  
// 最大重试次数和重试延迟时间
const MAX_RETRY_COUNT = 3;
const RETRY_DELAY = 300;

// 数据解析超时时间（毫秒）- 2分钟
const DATA_LOADING_TIMEOUT = 120000;

const MarkDown: React.FC<Props> = ({ markdown, fontSize = 14, loading = false, onApplyAuth }) => {
  const [streamItems, setStreamItems] = useState<StreamItem[]>([]);
  const [processedLength, setProcessedLength] = useState(0);
  const chartRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const chartInstances = useRef<Map<string, echarts.ECharts>>(new Map());
  const retryCounts = useRef<Map<string, number>>(new Map());
  const resizeObservers = useRef<Map<string, ResizeObserver>>(new Map());
  const renderedCharts = useRef<Set<string>>(new Set());
  const { state: streamingState } = useStreaming();

  // 数据解析超时管理
  const dataLoadingTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const [timedOutDataLoadings, setTimedOutDataLoadings] = useState<Set<string>>(new Set());

  const prefixCls = `${CLS_PREFIX}-markdown`;

  // 处理数据解析超时
  const handleDataLoadingTimeout = useCallback((dataLoadingId: string) => {
    console.warn(`数据解析超时: ${dataLoadingId}`);
    setTimedOutDataLoadings(prev => new Set(Array.from(prev).concat(dataLoadingId)));

    // 清理超时定时器
    const timeout = dataLoadingTimeouts.current.get(dataLoadingId);
    if (timeout) {
      clearTimeout(timeout);
      dataLoadingTimeouts.current.delete(dataLoadingId);
    }
  }, []);

  // 启动数据解析超时定时器
  const startDataLoadingTimeout = useCallback((dataLoadingId: string) => {
    // 清理之前的定时器（如果存在）
    const existingTimeout = dataLoadingTimeouts.current.get(dataLoadingId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // 设置新的超时定时器
    const timeout = setTimeout(() => {
      handleDataLoadingTimeout(dataLoadingId);
    }, DATA_LOADING_TIMEOUT);

    dataLoadingTimeouts.current.set(dataLoadingId, timeout);
  }, [handleDataLoadingTimeout]);

  // 清理数据解析超时定时器
  const clearDataLoadingTimeout = useCallback((dataLoadingId: string) => {
    const timeout = dataLoadingTimeouts.current.get(dataLoadingId);
    if (timeout) {
      clearTimeout(timeout);
      dataLoadingTimeouts.current.delete(dataLoadingId);
    }

    // 从超时集合中移除
    setTimedOutDataLoadings(prev => {
      const newSet = new Set(prev);
      newSet.delete(dataLoadingId);
      return newSet;
    });
  }, []);
    
  useEffect(() => {
    if (markdown.length <= processedLength) return;

    // console.log(`解析markdown，长度: ${markdown.length}, 已处理: ${processedLength}`);

    // 重新解析整个markdown内容，而不是增量解析
    const items = parseStreamContent(markdown);

    // console.log(`解析结果:`, items.map(item => ({
    //   type: item.type,
    //   id: item.id,
    //   hasChartData: !!item.chartData,
    //   contentLength: item.content.length
    // })));

    // 清理之前的渲染状态，确保重新渲染时能正确处理
    renderedCharts.current.clear();

    setStreamItems(items);
    setProcessedLength(markdown.length);
  }, [markdown, processedLength]);

  // 监听流式状态变化，当流式输出完成时清理loading项
  useEffect(() => {
    if (!streamingState.isStreaming && streamItems.length > 0) {
      // 流式输出已完成，移除所有loading项
      const filteredItems = streamItems.filter(item => item.type !== 'loading');
      if (filteredItems.length !== streamItems.length) {
        setStreamItems(filteredItems);
      }
    }
  }, [streamingState.isStreaming, streamItems]);
  
  // 提取完整的JSON  
  const extractCompleteJson = (text: string): { json: string; endIndex: number } | null => {  
    const trimmed = text.trim();  
      
    // 查找 JSON 开始位置（支持前面有其他文本的情况）  
    let startIndex = trimmed.indexOf('{');  
    if (startIndex === -1) {  
      return null;  
    }  
      
    const jsonStart = trimmed.substring(startIndex);  
    let braceCount = 0;  
    let inString = false;  
    let escaped = false;  
      
    for (let i = 0; i < jsonStart.length; i++) {  
      const char = jsonStart[i];  
        
      if (escaped) {  
        escaped = false;  
        continue;  
      }  
        
      if (char === '\\') {  
        escaped = true;  
        continue;  
      }  
        
      if (char === '"') {  
        inString = !inString;  
        continue;  
      }  
        
      if (!inString) {  
        if (char === '{') {  
          braceCount++;  
        } else if (char === '}') {  
          braceCount--;  
          // 当括号平衡时，找到完整的 JSON  
          if (braceCount === 0) {  
            const jsonString = jsonStart.substring(0, i + 1);  
            try {  
              JSON.parse(jsonString);  
              return {  
                json: jsonString,  
                endIndex: startIndex + i + 1  
              };  
            } catch (e) {  
              // JSON 无效，继续查找  
              continue;  
            }  
          }  
        }  
      }  
    }  
      
    return null;  
  };
    
  // 解析流式内容 - 重新设计以处理不完整的JSON和数据加载标识
  const parseStreamContent = (fullContent: string): StreamItem[] => {
    const items: StreamItem[] = [];
    let currentPos = 0;
    let waitingStartCount = 0; // 跟踪遇到的waitingStart数量
    let waitingEndCount = 0;   // 跟踪遇到的waitingEnd数量

    // 清理所有超时的数据加载项
    const currentTimedOutDataLoadings = Array.from(timedOutDataLoadings);

    while (currentPos < fullContent.length) {
      // 查找下一个特殊标识的位置
      const nextWaitingStart = fullContent.indexOf('waitingStart', currentPos);
      const nextWaitingEnd = fullContent.indexOf('waitingEnd', currentPos);
      const nextChart = fullContent.indexOf('isecharts', currentPos);

      // 找到最近的特殊标识
      const positions = [
        { pos: nextWaitingStart, type: 'waitingStart' },
        { pos: nextWaitingEnd, type: 'waitingEnd' },
        { pos: nextChart, type: 'chart' }
      ].filter(item => item.pos !== -1).sort((a, b) => a.pos - b.pos);

      const nextSpecial = positions[0];

      // 处理当前位置到下一个特殊标识之间的文本
      const textEnd = nextSpecial ? nextSpecial.pos : fullContent.length;
      if (textEnd > currentPos) {
        const textContent = fullContent.substring(currentPos, textEnd);
        if (textContent.trim()) {
          items.push({
            type: 'text',
            content: textContent,
            id: `text-${currentPos}-${items.length}`
          });
        }
      }

      if (!nextSpecial) {
        // 没有更多特殊标识，结束处理
        break;
      }

      // 处理特殊标识
      if (nextSpecial.type === 'waitingStart') {
        waitingStartCount++;
        // 创建数据加载项ID
        const dataLoadingId = `dataLoading-${nextSpecial.pos}-${items.length}`;

        // 添加数据加载项
        items.push({
          type: 'dataLoading',
          content: currentTimedOutDataLoadings.includes(dataLoadingId)
            ? '暂时无法解析，请重新提问...'
            : '数据解析中...',
          id: dataLoadingId
        });

        // 启动超时定时器（如果尚未超时）
        if (!currentTimedOutDataLoadings.includes(dataLoadingId)) {
          startDataLoadingTimeout(dataLoadingId);
        }

        currentPos = nextSpecial.pos + 'waitingStart'.length;
      } else if (nextSpecial.type === 'waitingEnd') {
        waitingEndCount++;
        // waitingEnd标记数据加载结束，移除最后一个dataLoading项（如果存在）
        const lastItemIndex = items.length - 1;
        if (lastItemIndex >= 0 && items[lastItemIndex].type === 'dataLoading') {
          // 清理该数据加载项的超时定时器
          clearDataLoadingTimeout(items[lastItemIndex].id);
          // 移除数据加载项
          items.splice(lastItemIndex, 1);
        }
        currentPos = nextSpecial.pos + 'waitingEnd'.length;
      } else if (nextSpecial.type === 'chart') {
        // 处理图表
        const jsonStart = nextSpecial.pos + 'isecharts'.length;
        const jsonMatch = extractCompleteJson(fullContent.substring(jsonStart));

        if (jsonMatch) {
          try {
            const chartData = JSON.parse(jsonMatch.json) as ChartData;
            if (isValidChartData(chartData)) {
              items.push({
                type: 'chart',
                content: '',
                chartData,
                id: `chart-${jsonStart}-${items.length}`
              });
              currentPos = jsonStart + jsonMatch.endIndex;
            } else {
              // 数据验证失败，显示loading并跳过这个位置
              items.push({
                type: 'loading',
                content: 'loading...',
                id: `loading-${jsonStart}-${items.length}`
              });
              currentPos = jsonStart + 1;
            }
          } catch (error) {
            // JSON解析失败，显示loading并跳过这个位置
            items.push({
              type: 'loading',
              content: 'loading...',
              id: `loading-${jsonStart}-${items.length}`
            });
            currentPos = jsonStart + 1;
          }
        } else {
          // 没有找到完整的JSON，可能是流式数据还没完整
          // 检查是否有部分JSON开始
          const partialJsonStart = fullContent.substring(jsonStart).indexOf('{');
          if (partialJsonStart !== -1) {
            // 有JSON开始但不完整，显示loading
            // 使用固定的ID，这样在后续解析中可以被替换
            items.push({
              type: 'loading',
              content: 'loading...',
              id: `chart-loading-${nextSpecial.pos}`
            });
            // 跳过到JSON开始位置，避免重复处理
            currentPos = jsonStart + partialJsonStart + 1;
          } else {
            // 没有JSON开始，作为普通文本处理
            const textContent = fullContent.substring(nextSpecial.pos, nextSpecial.pos + 'isecharts'.length + 1);
            items.push({
              type: 'text',
              content: textContent,
              id: `text-${nextSpecial.pos}-${items.length}`
            });
            currentPos = jsonStart + 1;
          }
        }
      }
    }

    // 如果还有未配对的waitingStart，在最后添加一个loading或超时消息
    if (waitingStartCount > waitingEndCount) {
      // 检查最后一个item是否已经是dataLoading，避免重复
      const lastItem = items[items.length - 1];
      if (!lastItem || lastItem.type !== 'dataLoading') {
        const dataLoadingId = `dataLoading-final-${Date.now()}`;

        // 添加数据加载项或超时消息
        items.push({
          type: 'dataLoading',
          content: currentTimedOutDataLoadings.includes(dataLoadingId)
            ? '暂时无法解析，请重新提问...'
            : '数据解析中...',
          id: dataLoadingId
        });

        // 启动超时定时器（如果尚未超时）
        if (!currentTimedOutDataLoadings.includes(dataLoadingId)) {
          startDataLoadingTimeout(dataLoadingId);
        }
      }
    }

    // 最后清理：如果没有更多的isecharts标识，移除所有loading项
    // 这表示流式输出已经完成，不应该再有loading状态
    const hasIncompleteCharts = fullContent.includes('isecharts') &&
                               items.some(item => item.type === 'loading');

    if (hasIncompleteCharts) {
      // 检查是否还有未完成的图表JSON
      const lastIsechartsIndex = fullContent.lastIndexOf('isecharts');
      if (lastIsechartsIndex !== -1) {
        const afterLastIsecharts = fullContent.substring(lastIsechartsIndex + 'isecharts'.length);
        const hasCompleteJson = extractCompleteJson(afterLastIsecharts);

        // 如果最后一个isecharts后面没有完整的JSON，保留loading
        // 否则移除所有loading项（表示流式输出已完成）
        if (hasCompleteJson || !afterLastIsecharts.includes('{')) {
          // 移除所有loading项，因为流式输出已完成
          return items.filter(item => item.type !== 'loading');
        }
      }
    }

    return items;
  };
  
  // 验证图表数据完整性
  const isValidChartData = (chartData: ChartData): boolean => {
    if (!chartData.chartType || !chartData.chartTitle) {
      return false;
    }

    switch (chartData.chartType) {
      case 'bar':
      case 'line':
        return Array.isArray(chartData.chartXAxis) &&
              Array.isArray(chartData.chartData) &&
              chartData.chartXAxis.length > 0 &&
              chartData.chartData.length > 0 &&
              (chartData.chartData as number[]).every(item => typeof item === 'number');
      case 'pie':
        return Array.isArray(chartData.chartData) &&
              chartData.chartData.length > 0 &&
              (chartData.chartData as Array<{name: string; value: number}>).every(item =>
                typeof item === 'object' &&
                'name' in item &&
                'value' in item
              );
      case 'multiLine':
      case 'multiBar':
        return Array.isArray(chartData.chartXAxis) &&
              Array.isArray(chartData.chartData) &&
              chartData.chartXAxis.length > 0 &&
              chartData.chartData.length > 0 &&
              (chartData.chartData as MultiSeriesData[]).every(series =>
                typeof series === 'object' &&
                'name' in series &&
                'data' in series &&
                Array.isArray(series.data) &&
                series.data.every(val => typeof val === 'number')
              );
      default:
        return false;
    }
  };
    
  // 渲染图表函数（带重试机制和容器检查）
  const renderChart = useCallback((chartData: ChartData, chartId: string) => {
    const retry = (delay = RETRY_DELAY) => {
      const count = retryCounts.current.get(chartId) || 0;
      if (count >= MAX_RETRY_COUNT) {
        console.warn(`图表 ${chartId} 渲染失败，已达到最大重试次数`);
        return;
      }

      retryCounts.current.set(chartId, count + 1);
      setTimeout(() => renderChart(chartData, chartId), delay * (count + 1));
    };

    // 使用requestAnimationFrame确保在正确时机渲染
    requestAnimationFrame(() => {
      const chartRef = chartRefs.current.get(chartId);
      if (!chartRef) {
        console.warn(`图表容器 ${chartId} 未找到，准备重试`);
        retry();
        return;
      }

      // 检查容器是否有有效尺寸
      if (chartRef.offsetWidth === 0 || chartRef.offsetHeight === 0) {
        console.warn(`图表容器 ${chartId} 尺寸无效，准备重试`);
        retry();
        return;
      }

      // 检查是否已经渲染过
      if (renderedCharts.current.has(chartId)) {
        return;
      }

      let instance = chartInstances.current.get(chartId);
      if (!instance) {
        try {
          instance = echarts.init(chartRef);
          chartInstances.current.set(chartId, instance);

          // 添加resize监听
          const observer = new ResizeObserver(() => {
            instance?.resize();
          });
          observer.observe(chartRef);
          resizeObservers.current.set(chartId, observer);
        } catch (error) {
          console.error(`初始化图表 ${chartId} 失败:`, error);
          retry();
          return;
        }
      }

      try {
        const option = getChartOption(chartData);
        instance.setOption(option, true); // 使用true强制不合并选项
        instance.resize();

        // 标记为已渲染
        renderedCharts.current.add(chartId);
        retryCounts.current.delete(chartId); // 重置重试计数

        // console.log(`图表 ${chartId} 渲染成功`);
      } catch (error) {
        console.error(`设置图表选项失败 ${chartId}:`, error);
        retry();
      }
    });
  }, []);
    
  const getChartOption = (chartData: ChartData) => {    
    const { chartType, chartTitle, chartXAxis, chartData: data } = chartData;    
    
    const baseOption = {    
      title: { 
        text: chartTitle,
        left: 'center',
        top: '5px'
      },
      backgroundColor: '#F5F8FB',   
      tooltip: { trigger: chartType === 'pie' ? 'item' : 'axis' },    
      grid: chartType !== 'pie' ? {    
        left: '3%',    
        right: '4%',    
        bottom: '3%',    
        top: '20%',    
        containLabel: true    
      } : undefined
    };    
    
    if (chartType === 'bar') {    
      return {    
        ...baseOption,    
        xAxis: { 
          type: 'category', 
          data: chartData.chartXAxis,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            width: 200,
            overflow: 'truncate',
            showMaxLabel: true,
            hideOverlap: false,
            interval: 0,
            color: '#333',
            rotate: 30,
          },
        },
        yAxis: { 
          type: 'value',
        },
        series: [{
          type: 'bar',
          name: chartData.chartTitle,
          data: chartData.chartData as number[],
          barWidth: 20,
          itemStyle: {
            borderRadius: [10, 10, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: CHART_BLUE_COLOR },
              { offset: 1, color: getChartLightenColor(CHART_BLUE_COLOR) },
            ]),
          },
        }]   
      };    
    }    
    
    if (chartType === 'pie') {    
       // 确保数据格式正确  
      const pieData = Array.isArray(data) && data.length > 0 && typeof data[0] === 'object'   
        ? data as Array<{name: string; value: number}>  
        : [];  
        
      if (pieData.length === 0) {  
        console.warn('饼状图数据格式不正确或为空');  
        return baseOption;  
      }  
    
      return {  
        ...baseOption,  
        legend: {  
          orient: 'vertical',  
          left: 'left',  
          top: 40,
          type: 'scroll',  
          data: pieData.map(item => item.name),  
          selectedMode: true,  
          textStyle: {  
            color: '#666',  
          },  
        },  
        series: [{  
          name: '占比',  
          type: 'pie',  
          radius: ['40%', '70%'],  
          avoidLabelOverlap: false,  
          itemStyle: {  
            borderRadius: 10,  
            borderColor: '#fff',  
            borderWidth: 2,  
          },  
          data: pieData.map((item, index) => ({  
            ...item,  
            itemStyle: { color: THEME_COLOR_LIST[index % THEME_COLOR_LIST.length] }  
          })),  
          emphasis: {  
            itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' }  
          }  
        }]  
      };      
    }    
    
    if (chartType === 'line') {
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: chartXAxis
        },
        yAxis: { type: 'value' },
        series: [{
          type: 'line',
          data: data as number[],
          smooth: true,
          itemStyle: { color: THEME_COLOR_LIST[0] }
        }]
      };
    }

    // 多折线图
    if (chartType === 'multiLine') {
      const multiLineData = data as MultiSeriesData[];
      return {
        ...baseOption,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: multiLineData.map(series => series.name),
          top: 30,
          textStyle: {
            color: '#666'
          }
        },
        xAxis: {
          type: 'category',
          data: chartXAxis,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            color: '#333',
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            color: '#333',
          },
          splitLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
        },
        series: multiLineData.map((series, index) => ({
          name: series.name,
          type: 'line',
          data: series.data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: THEME_COLOR_LIST[index % THEME_COLOR_LIST.length]
          },
          itemStyle: {
            color: THEME_COLOR_LIST[index % THEME_COLOR_LIST.length]
          },
          emphasis: {
            focus: 'series'
          }
        }))
      };
    }

    // 多柱状图
    if (chartType === 'multiBar') {
      const multiBarData = data as MultiSeriesData[];
      return {
        ...baseOption,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: multiBarData.map(series => series.name),
          top: 30,
          textStyle: {
            color: '#666'
          }
        },
        xAxis: {
          type: 'category',
          data: chartXAxis,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            color: '#333',
            interval: 0,
            rotate: 30,
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
          axisLabel: {
            color: '#333',
          },
          splitLine: {
            lineStyle: {
              color: CHART_SECONDARY_COLOR,
            },
          },
        },
        series: multiBarData.map((series, index) => ({
          name: series.name,
          type: 'bar',
          data: series.data,
          barWidth: 20,
          itemStyle: {
            borderRadius: [10, 10, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: THEME_COLOR_LIST[index % THEME_COLOR_LIST.length] },
              { offset: 1, color: getChartLightenColor(THEME_COLOR_LIST[index % THEME_COLOR_LIST.length]) },
            ]),
          },
          emphasis: {
            focus: 'series'
          }
        }))
      };
    }

    return baseOption;
  };    
    
  // 渲染图表
  useEffect(() => {
    streamItems.forEach(item => {
      if (item.type === 'chart' && item.chartData && !renderedCharts.current.has(item.id)) {
        // 延迟渲染，确保DOM已经挂载
        setTimeout(() => {
          renderChart(item.chartData!, item.id);
        }, 100);
      }
    });
  }, [streamItems, renderChart]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理所有图表实例和观察器
      chartInstances.current.forEach((instance, id) => {
        resizeObservers.current.get(id)?.disconnect();
        instance?.dispose();
      });
      chartInstances.current.clear();
      chartRefs.current.clear();
      resizeObservers.current.clear();
      retryCounts.current.clear();
      renderedCharts.current.clear();

      // 清理所有数据解析超时定时器
      dataLoadingTimeouts.current.forEach((timeout) => {
        clearTimeout(timeout);
      });
      dataLoadingTimeouts.current.clear();
    };
  }, []);
  
  // 合并连续的文本项目，避免不必要的换行  
  const mergeConsecutiveTextItems = (items: StreamItem[]) => {  
    const merged: StreamItem[] = [];  
    let currentTextContent = '';  
    let currentTextId = '';  
  
    items.forEach((item, index) => {  
      if (item.type === 'text') {  
        if (currentTextContent === '') {  
          currentTextId = item.id;  
        }  
        currentTextContent += item.content;  
          
        // 检查下一个项目是否也是文本  
        const nextItem = items[index + 1];  
        if (!nextItem || nextItem.type !== 'text') {  
          // 当前文本块结束，添加到结果中  
          merged.push({  
            type: 'text',  
            content: currentTextContent,  
            id: currentTextId  
          });  
          currentTextContent = '';  
          currentTextId = '';  
        }  
      } else {  
        // 非文本项目直接添加  
        merged.push(item);  
      }  
    });  
  
    return merged;  
  };  
  
  const mergedItems = mergeConsecutiveTextItems(streamItems);  
    
  return (    
    <div className={`${prefixCls} markdown-body`} style={{ fontSize: fontSize }}>    
      {mergedItems.map(item => {    
        if (item.type === 'text') {    
          return (    
            <Markdown   
              key={item.id}   
              rehypePlugins={[rehypeHighlight]}   
              remarkPlugins={[remarkGfm]}  
            >  
              {item.content}  
            </Markdown>  
          );    
        }    
    
        if (item.type === 'loading') {
          return (
            <div key={item.id} className="loading-container" style={{ padding: '16px', textAlign: 'center' }}>
              图表生成中<Loading />
            </div>
          );
        }

        if (item.type === 'dataLoading') {
          const isTimedOut = timedOutDataLoadings.has(item.id);
          return (
            <div key={item.id} className="data-loading-container" style={{ padding: '16px', textAlign: 'center' }}>
              {isTimedOut ? (
                '暂时无法解析，请重新提问'
              ) : (
                <>数据解析中<Loading /></>
              )}
            </div>
          );
        }

        if (item.type === 'chart') {
          return (
            <div key={item.id} className="chart-container" style={{ height: '350px', margin: '16px 0', minHeight: '350px' }}>
              <div
                ref={ref => {
                  if (ref) {
                    chartRefs.current.set(item.id, ref);
                    // 确保容器有明确的尺寸
                    ref.style.width = '100%';
                    ref.style.height = '100%';
                    ref.style.minHeight = '350px';
                  }
                }}
                style={{ width: '100%', height: '100%', minHeight: '350px' }}
              />
            </div>
          );
        }
    
        return null;    
      })}    
    </div>    
  );    
};    
    
export default MarkDown;