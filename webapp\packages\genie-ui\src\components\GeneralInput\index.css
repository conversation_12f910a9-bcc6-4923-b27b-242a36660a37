/* GeneralInput 组件样式 */
.general-input-container .ant-input {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 12px !important;
}

.general-input-container .ant-input::placeholder {
  line-height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

/* 确保TextArea在容器中垂直居中 */
.general-input-container {
  display: flex;
  align-items: center;
}
