import { Space, Spin, Switch, message } from 'antd';
import { CheckCircleFilled, InfoCircleOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { PREFIX_CLS, MsgContentTypeEnum } from '../../common/constants';
import { MsgDataType } from '../../common/type';
import ChatMsg from '../ChatMsg';
import WebPage from '../ChatMsg/WebPage';
import Loading from './Loading';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import React, { ReactNode, useState, useEffect } from 'react';

import MarkdownRenderer from '../MarkdownRenderer/index'; // Li Wei - 引入自己封装的markdown组件


type Props = {
  queryId?: number;
  question: string;
  queryMode?: string;
  executeLoading: boolean;
  entitySwitchLoading?: boolean;
  chartIndex: number;
  executeTip?: string;
  executeErrorMsg?: string;
  executeItemNode?: ReactNode;
  renderCustomExecuteNode?: boolean;
  data?: MsgDataType;
  triggerResize?: boolean;
  isDeveloper?: boolean;
  isSimpleMode?: boolean;
  forceChartType?: MsgContentTypeEnum | null;
};

const ExecuteItem: React.FC<Props> = ({
  queryId,
  question,
  queryMode,
  executeLoading,
  entitySwitchLoading = false,
  chartIndex,
  executeTip,
  executeErrorMsg,
  executeItemNode,
  renderCustomExecuteNode,
  data,
  triggerResize,
  isDeveloper,
  isSimpleMode,
  forceChartType,
}) => {
  const prefixCls = `${PREFIX_CLS}-item`;
  const [showMsgContentTable, setShowMsgContentTable] = useState<boolean>(false);
  const [msgContentType, setMsgContentType] = useState<MsgContentTypeEnum>();
  const [showErrMsg, setShowErrMsg] = useState<boolean>(false);

  // 当forceChartType变化时，重置Switch状态
  useEffect(() => {
    if (forceChartType) {
      setShowMsgContentTable(false);
    }
  }, [forceChartType]);
  const titlePrefix = queryMode === 'PLAIN_TEXT' || queryMode === 'WEB_SERVICE' ? '问答' : '数据';

  const [collapsed, setCollapsed] = useState(false);

  const getNodeTip = (title: ReactNode, tip?: string | ReactNode) => {
    return (
      <>
        <div className={`${prefixCls}-title-bar`}>
          <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          <div className={`${prefixCls}-step-title`}>
            {title}
            {!tip && <Loading />}
          </div>
          <span
            onClick={() => setCollapsed(!collapsed)}
            style={{ cursor: 'pointer', marginLeft: 8, color: 'var(--text-color-third)' }}
          >
            {collapsed ? <DownOutlined /> : <UpOutlined />}
          </span>
        </div>
        {tip && !collapsed && <div className={`${prefixCls}-content-container`}>{tip}</div>}
      </>
    );
  };

  if (executeLoading) {
    return getNodeTip(`${titlePrefix}查询中`);
  }

  const handleCopy = (_: string, result: any) => {
    result ? message.success('复制SQL成功', 1) : message.error('复制SQL失败', 1);
  };

  if (executeTip) {
    return getNodeTip(
      <>
        <span>{titlePrefix}查询失败</span>
        {executeErrorMsg && (
          <Space>
            <InfoCircleOutlined style={{ marginLeft: 5, color: 'red' }} />
            <a
              onClick={() => {
                setShowErrMsg(!showErrMsg);
              }}
            >
              {!showErrMsg ? '查看' : '收起'}
            </a>
          </Space>
        )}
        {!!data?.queryTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {data.queryTimeCost}ms)</span>
        )}
      </>,

      <>
        {showErrMsg && (
          <SyntaxHighlighter className={`${prefixCls}-code`} language="sql" style={solarizedlight}>
            {executeErrorMsg}
          </SyntaxHighlighter>
        )}
      </>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <>
      {!isSimpleMode && (
        <div className={`${prefixCls}-title-bar`}>
          <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          <div
            className={`${prefixCls}-step-title ${prefixCls}-execute-title-bar`}
          >
            <div style={{display: 'flex',alignItems: 'center'}}>
              {titlePrefix}查询结果
              {!!data?.queryTimeCost && isDeveloper && (
                <span className={`${prefixCls}-title-tip`}>(耗时: {data.queryTimeCost}ms)</span>
              )}
            </div>
          </div>
          {/* 折叠/展开箭头 */}
          <span
            onClick={() => setCollapsed(!collapsed)}
            style={{ cursor: 'pointer', marginLeft: 8, color: 'var(--text-color-third)' }}
          >
            {collapsed ? <DownOutlined /> : <UpOutlined />}
          </span>
          <div style={{marginLeft: 10}}>
            {[MsgContentTypeEnum.METRIC_TREND, MsgContentTypeEnum.METRIC_BAR, MsgContentTypeEnum.METRIC_PIE].includes(
              msgContentType as MsgContentTypeEnum
            ) && (
              <Switch
                checkedChildren="表格"
                unCheckedChildren="表格"
                onChange={checked => {
                  setShowMsgContentTable(checked);
                }}
              />
            )}
          </div>
        </div>
      )}

      {!collapsed && (
        <div
          className={`${prefixCls}-content-container ${
            isSimpleMode ? `${prefixCls}-content-container-simple` : ''
          }`}
          style={{ borderLeft: queryMode === 'PLAIN_TEXT' ? 'none' : undefined }}
        >
          <Spin spinning={entitySwitchLoading}>
            {data.queryAuthorization?.message && (
              <div className={`${prefixCls}-auth-tip`}>提示：{data.queryAuthorization.message}</div>
            )}

            {renderCustomExecuteNode && executeItemNode ? (
              executeItemNode
            ) : data?.queryMode === 'PLAIN_TEXT' || data?.queryMode === 'WEB_SERVICE' ? (
                <MarkdownRenderer text={data?.textResult || ''} />
            ) : data?.queryMode === 'WEB_PAGE' ? (
              <WebPage id={queryId!} data={data} />
            ) : (
              <ChatMsg
                isSimpleMode={isSimpleMode}
                forceShowTable={showMsgContentTable}
                queryId={queryId}
                question={question}
                data={data}
                chartIndex={chartIndex}
                triggerResize={triggerResize}
                onMsgContentTypeChange={setMsgContentType}
                forceChartType={forceChartType}
              />
            )}
          </Spin>
        </div>
      )}
    </>
  );
};

export default ExecuteItem;
