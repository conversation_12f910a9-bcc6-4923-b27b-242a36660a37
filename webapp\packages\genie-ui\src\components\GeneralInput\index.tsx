import React, { useRef, useState } from "react";
import { Input, Tooltip } from "antd";
import classNames from "classnames";
import { TextAreaRef } from "antd/es/input/TextArea";
import "./index.css";

const { TextArea } = Input;

type Props = {
  placeholder: string;
  showBtn: boolean;
  disabled: boolean;
  size: string;
  product?: any;
  send: (p: any) => void;
};

const GeneralInput: React.FC<Props> = (props) => {
  const { placeholder, disabled, product, send } = props;
  const [question, setQuestion] = useState<string>("");
  const [deepThink, setDeepThink] = useState<boolean>(false);
  const textareaRef = useRef<TextAreaRef>(null);
  const tempData = useRef<{
    cmdPress?: boolean;
    compositing?: boolean;
  }>({});

  const questionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuestion(e.target.value);
  };

  const pressEnter: React.KeyboardEventHandler<HTMLTextAreaElement> = () => {
    if (tempData.current.compositing) {
      return;
    }
    // 按住command 回车换行逻辑
    if (tempData.current.cmdPress) {
      const textareaDom = textareaRef.current?.resizableTextArea?.textArea;
      if (!textareaDom) {
        return;
      }
      const { selectionStart, selectionEnd } = textareaDom || {};
      const newValue =
        question.substring(0, selectionStart) +
        '\n' + // 插入换行符
        question.substring(selectionEnd!);

      setQuestion(newValue);
      setTimeout(() => {
        textareaDom.selectionStart = selectionStart! + 1;
        textareaDom.selectionEnd = selectionStart! + 1;
        textareaDom.focus();
      }, 20);
      return;
    }
    // 屏蔽状态，不发
    if (!question || disabled) {
      return;
    }
    send({
      message: question,
      outputStyle: product?.type,
      deepThink,
    });
    setQuestion("");
  };

  const sendMessage = () => {
    send({
      message: question,
      outputStyle: product?.type,
      deepThink,
    });
    setQuestion("");
  };

  return (
    <div className="relative w-full">
      <div className="general-input-container relative w-full h-[56px] bg-white border border-[#4A72F5] rounded-[8px] transition-all duration-150 ease-in-out hover:border-[#1890ff] hover:shadow-[0_0_0_2px_rgba(24,144,255,0.2)] focus-within:border-[#1890ff] focus-within:shadow-[0_0_0_2px_rgba(24,144,255,0.2)] flex items-center">
        <TextArea
          ref={textareaRef}
          value={question}
          placeholder={placeholder}
          rows={1}
          autoSize={false}
          className={classNames(
            "w-full border-0 resize-none bg-transparent outline-none px-3 text-[14px]"
          )}
          style={{
            boxShadow: 'none',
            border: 'none',
            outline: 'none',
            height: '32px',
            lineHeight: '32px',
            paddingTop: '0',
            paddingBottom: '0',
            display: 'flex',
            alignItems: 'center'
          }}
          onChange={questionChange}
          onPressEnter={pressEnter}
          onKeyDown={(event) => {
            tempData.current.cmdPress = event.metaKey || event.ctrlKey;
          }}
          onKeyUp={() => {
            tempData.current.cmdPress = false;
          }}
          onCompositionStart={() => {
            tempData.current.compositing = true;
          }}
          onCompositionEnd={() => {
            tempData.current.compositing = false;
          }}
        />

        {/* 发送按钮 */}
        <div
          className={classNames(
            "absolute right-[10px] bottom-[13px] flex items-center justify-center w-[30px] h-[30px] rounded-full transition-all duration-300 ease-in-out",
            !question || disabled
              ? "bg-[rgb(184,184,191)] cursor-not-allowed"
              : "bg-[var(--chat-blue)] cursor-pointer"
          )}
          onClick={!question || disabled ? undefined : sendMessage}
        >
          <Tooltip title="发送">
            <svg
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              style={{ fill: '#fff' }}
            >
              <path d="M1007.4 16.5c-7.2-7.2-18-9.4-27.3-5.4L24.1 423.7c-8.9 3.8-14.8 12.3-15 22-0.3 9.7 5.1 18.3 13.7 22.6l354.6 177.4 180.9 358.9c4.2 8.4 12.8 9.4 22.1 9.4 0.3 0 0.5 0 0.8 0 9.6 0 18.2-1.9 22-10.8l409.5-959.3C1016.7 34.5 1014.6 23.7 1007.4 16.5zM92.8 448.7 913.2 94.5 401.3 603.9 92.8 448.7zM578.5 938.6l-159-317.6L929 114 578.5 938.6z" />
            </svg>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default GeneralInput;
